import React, { useState, useMemo, useEffect, useRef, useCallback } from 'react';
import {
  DndContext,
  DragEndEvent,
  DragOverlay,
  DragStartEvent,
  PointerSensor,
  useSensor,
  useSensors,
  closestCorners,
  useDroppable,
  useDraggable,
} from '@dnd-kit/core';
import {
  Clock,
  Filter,
  ChevronLeft,
  ChevronRight,
  Pause,
  CheckCircle2,
  Archive,
  Plus,
  Calendar,
  ZoomIn,
  ZoomOut,
  Edit3,
} from 'lucide-react';
import { Task } from '@/services/api';
import { useTaskStore } from '@/store/taskStore';
import { useProjectStore } from '@/store/projectStore';
import { useTagStore } from '@/store/tagStore';
import Button from '@/components/ui/Button';
import TagBadge from '@/components/Tags/TagBadge';
import CollapsibleTaskSection from './CollapsibleTaskSection';

interface TimelineProps {
  currentDate: Date;
  onDateChange: (date: Date) => void;
}

type ViewMode = '15min' | '30min' | '1hr' | '4hr' | '8hr' | 'daily';

// Helper function to format date as YYYY-MM-DD in local timezone
const formatLocalDate = (date: Date): string => {
  const year = date.getFullYear();
  const month = (date.getMonth() + 1).toString().padStart(2, '0');
  const day = date.getDate().toString().padStart(2, '0');
  return `${year}-${month}-${day}`;
};

// Generate time slots based on view mode
const generateTimeSlots = (date: Date, viewMode: ViewMode) => {
  const slots = [];
  const dateStr = formatLocalDate(date);

  switch (viewMode) {
    case '15min':
      for (let hour = 0; hour < 24; hour++) {
        for (let minute = 0; minute < 60; minute += 15) {
          const slotDate = new Date(date);
          slotDate.setHours(hour, minute, 0, 0);
          const displayHour = hour === 0 ? 12 : hour > 12 ? hour - 12 : hour;
          const ampm = hour < 12 ? 'AM' : 'PM';
          slots.push({
            id: `${dateStr}-${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`,
            label: `${displayHour}:${minute.toString().padStart(2, '0')} ${ampm}`,
            date: slotDate,
            hour,
            minute,
          });
        }
      }
      break;

    case '30min':
      for (let hour = 0; hour < 24; hour++) {
        for (let minute = 0; minute < 60; minute += 30) {
          const slotDate = new Date(date);
          slotDate.setHours(hour, minute, 0, 0);
          const displayHour = hour === 0 ? 12 : hour > 12 ? hour - 12 : hour;
          const ampm = hour < 12 ? 'AM' : 'PM';
          slots.push({
            id: `${dateStr}-${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`,
            label: `${displayHour}:${minute.toString().padStart(2, '0')} ${ampm}`,
            date: slotDate,
            hour,
            minute,
          });
        }
      }
      break;

    case '1hr':
      for (let hour = 0; hour < 24; hour++) {
        const slotDate = new Date(date);
        slotDate.setHours(hour, 0, 0, 0);
        slots.push({
          id: `${dateStr}-${hour.toString().padStart(2, '0')}:00`,
          label: hour === 0 ? '12:00 AM' : hour < 12 ? `${hour}:00 AM` : hour === 12 ? '12:00 PM' : `${hour - 12}:00 PM`,
          date: slotDate,
          hour,
        });
      }
      break;

    case '4hr':
      for (let hour = 0; hour < 24; hour += 4) {
        const slotDate = new Date(date);
        slotDate.setHours(hour, 0, 0, 0);
        const endHour = Math.min(hour + 4, 24);
        slots.push({
          id: `${dateStr}-${hour.toString().padStart(2, '0')}:00`,
          label: `${hour.toString().padStart(2, '0')}:00-${endHour.toString().padStart(2, '0')}:00`,
          date: slotDate,
          hour,
          duration: 4,
        });
      }
      break;

    case '8hr':
      for (let hour = 0; hour < 24; hour += 8) {
        const slotDate = new Date(date);
        slotDate.setHours(hour, 0, 0, 0);
        const endHour = Math.min(hour + 8, 24);
        slots.push({
          id: `${dateStr}-${hour.toString().padStart(2, '0')}:00`,
          label: `${hour.toString().padStart(2, '0')}:00-${endHour.toString().padStart(2, '0')}:00`,
          date: slotDate,
          hour,
          duration: 8,
        });
      }
      break;

    case 'daily':
    default:
      const startOfWeek = new Date(date);
      startOfWeek.setDate(date.getDate() - date.getDay());
      for (let i = 0; i < 7; i++) {
        const slotDate = new Date(startOfWeek);
        slotDate.setDate(startOfWeek.getDate() + i);
        slots.push({
          id: formatLocalDate(slotDate),
          label: slotDate.toLocaleDateString('en-US', { weekday: 'short', month: 'short', day: 'numeric' }),
          date: slotDate,
          isToday: slotDate.toDateString() === new Date().toDateString(),
        });
      }
      break;
  }

  return slots;
};

// SIMPLE DRAG PREVIEW - CLEAN CENTERING
const StandardizedDragPreview: React.FC<{
  task: Task;
  textZoom: number;
  viewMode: ViewMode;
}> = ({ task, textZoom, viewMode }) => {
  // Calculate standard 1-hour width for consistent preview
  const getStandardWidth = () => {
    const baseWidth = viewMode === '15min' || viewMode === '30min' ? 120 :
                     viewMode === '1hr' ? 150 :
                     viewMode === '4hr' ? 180 :
                     viewMode === '8hr' ? 210 : 150;
    return baseWidth * textZoom;
  };

  return (
    <div
      className="pointer-events-none select-none"
      style={{
        width: getStandardWidth(),
        height: '60px',
        // Simple clean centering
        transform: 'translate(-50%, -50%) rotate(2deg) scale(1.05)',
        opacity: 0.95,
        zIndex: 10000,
      }}
    >
      <div
        className="
          relative p-3 rounded-lg border-2
          bg-white border-blue-400 shadow-2xl
          min-h-[60px] flex flex-col justify-between
        "
        style={{
          width: `${getStandardWidth()}px`,
          minWidth: '100px',
        }}
      >
        {/* Task Content - COMPACT DESIGN */}
        <div className="relative pointer-events-none select-none">
          <div className="flex items-start justify-between mb-1">
            <h4
              className="font-semibold text-gray-800 line-clamp-1 leading-tight"
              style={{ fontSize: `${0.75 * textZoom}rem` }}
            >
              {task.title}
            </h4>
            {task.project && (
              <div
                className="w-2 h-2 rounded-full flex-shrink-0 ml-2 mt-1"
                style={{ backgroundColor: task.project.color || '#64748b' }}
              />
            )}
          </div>

          {task.content && (
            <p className="text-xs text-gray-600 line-clamp-1 mb-1" style={{ fontSize: `${0.65 * textZoom}rem` }}>
              {task.content}
            </p>
          )}

          <div className="flex items-center justify-between">
            <div className="flex items-center gap-1">
              {task.project && (
                <span className="text-xs text-gray-500 font-medium" style={{ fontSize: `${0.6 * textZoom}rem` }}>
                  {task.project.name}
                </span>
              )}
              {task.tag && (
                <TagBadge tag={task.tag} size="sm" />
              )}
            </div>
            <span className="text-xs text-blue-600 font-medium" style={{ fontSize: `${0.6 * textZoom}rem` }}>
              1hr
            </span>
          </div>
        </div>

        {/* Status indicator */}
        <div className="absolute top-2 right-2 w-2 h-2 rounded-full bg-blue-500" />

        {/* Drag indicator */}
        <div className="absolute inset-0 border-2 border-dashed border-blue-300 rounded-lg pointer-events-none" />
      </div>
    </div>
  );
};

// Beautiful Timeline Task Component - COMPLETELY FIXED DRAG AND RESIZE
const TimelineTask: React.FC<{
  task: Task;
  textZoom: number;
  viewMode: ViewMode;
  onRightClick?: (e: React.MouseEvent, task: Task) => void;
  onResize?: (taskId: string, newDuration: number) => void;
}> = ({ task, textZoom, viewMode, onRightClick, onResize }) => {
  const [isResizing, setIsResizing] = useState(false);
  const [resizeStart, setResizeStart] = useState({ x: 0, width: 0 });
  const taskRef = useRef<HTMLDivElement>(null);

  // COMPLETELY SEPARATED DRAG IMPLEMENTATION - NO INTERFERENCE WITH RESIZE
  const { attributes, listeners, setNodeRef, transform, isDragging } = useDraggable({
    id: task.id,
    // Disable drag when resizing to prevent conflicts
    disabled: isResizing,
  });

  const style = transform ? {
    transform: `translate3d(${transform.x}px, ${transform.y}px, 0)`,
  } : undefined;

  // PROFESSIONAL TASK WIDTH CALCULATION - 1HR BASE WITH VISUAL EXPANSION
  const getTaskWidth = () => {
    const duration = task.effortEstimate || 60; // Default 1 hour in minutes
    const baseWidth = viewMode === '15min' || viewMode === '30min' ? 120 :
                     viewMode === '1hr' ? 150 :
                     viewMode === '4hr' ? 180 :
                     viewMode === '8hr' ? 210 : 150;

    // Calculate width based on duration relative to 1 hour
    const hourRatio = duration / 60;
    const calculatedWidth = baseWidth * hourRatio * textZoom;

    // Minimum width is always 1-hour base width, maximum scales with duration
    return Math.max(baseWidth * textZoom * 0.8, calculatedWidth);
  };

  // Calculate duration from width for resizing - UPDATED FOR NEW SIZES
  const getDurationFromWidth = useCallback((width: number) => {
    const adjustedWidth = width + 6; // Add back the gap (reduced from 8 to 6)
    switch (viewMode) {
      case '15min':
        return Math.max(15, Math.ceil((adjustedWidth / (120 * textZoom)) * 15)); // Updated to 120
      case '30min':
        return Math.max(30, Math.ceil((adjustedWidth / (120 * textZoom)) * 30)); // Updated to 120
      case '1hr':
        return Math.max(60, Math.ceil((adjustedWidth / (150 * textZoom)) * 60)); // Updated to 150
      case '4hr':
        return Math.max(240, Math.ceil((adjustedWidth / (180 * textZoom)) * 240)); // Updated to 180
      case '8hr':
        return Math.max(480, Math.ceil((adjustedWidth / (210 * textZoom)) * 480)); // Updated to 210
      default:
        return Math.max(60, Math.ceil((adjustedWidth / (150 * textZoom)) * 60)); // Updated to 150
    }
  }, [viewMode, textZoom]);

  // COMPLETELY SEPARATED RESIZE HANDLERS - NO DRAG INTERFERENCE
  const handleResizeStart = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();

    // CRITICAL: Completely stop all event propagation
    e.nativeEvent.stopImmediatePropagation();

    if (!taskRef.current) return;

    // Set resizing state to disable drag
    setIsResizing(true);
    setResizeStart({
      x: e.clientX,
      width: taskRef.current.offsetWidth,
    });

    // Add visual feedback
    document.body.style.cursor = 'ew-resize';
    document.body.style.userSelect = 'none';
  };

  const handleResizeMove = useCallback((e: MouseEvent) => {
    if (!isResizing || !taskRef.current) return;

    e.preventDefault();
    e.stopPropagation();

    const deltaX = e.clientX - resizeStart.x;
    const newWidth = Math.max(100, resizeStart.width + deltaX); // Minimum 100px width

    // Apply width change immediately for visual feedback
    taskRef.current.style.width = `${newWidth}px`;
  }, [isResizing, resizeStart.x, resizeStart.width]);

  const handleResizeEnd = useCallback(() => {
    if (!isResizing || !taskRef.current) return;

    const newWidth = taskRef.current.offsetWidth;
    const newDuration = getDurationFromWidth(newWidth);

    // Reset cursor and selection
    document.body.style.cursor = '';
    document.body.style.userSelect = '';

    setIsResizing(false);

    // Call the resize callback with rounded duration
    if (onResize) {
      onResize(task.id, Math.round(newDuration));
    }
  }, [isResizing, onResize, task.id, getDurationFromWidth]);

  // Add global mouse event listeners for resizing - COMPLETELY ISOLATED
  useEffect(() => {
    if (isResizing) {
      document.addEventListener('mousemove', handleResizeMove, { capture: true });
      document.addEventListener('mouseup', handleResizeEnd, { capture: true });

      return () => {
        document.removeEventListener('mousemove', handleResizeMove, { capture: true });
        document.removeEventListener('mouseup', handleResizeEnd, { capture: true });
        // Cleanup cursor styles
        document.body.style.cursor = '';
        document.body.style.userSelect = '';
      };
    }
  }, [isResizing, handleResizeMove, handleResizeEnd]);

  return (
    <div
      ref={setNodeRef}
      style={style}
      className={`
        transition-all duration-200 select-none
        ${isDragging ? 'opacity-0' : ''}
        ${isResizing ? 'z-50' : 'z-10'}
      `}
    >
      {/* MAIN TASK CONTAINER - COMPLETELY SEPARATED DRAG AND RESIZE */}
      <div
        ref={taskRef}
        onContextMenu={(e) => onRightClick?.(e, task)}
        className={`
          relative p-3 rounded-lg border-2 group select-none
          bg-white hover:bg-gray-50 border-gray-200 hover:border-gray-300
          hover:shadow-lg transition-all duration-200
          min-h-[60px] flex flex-col justify-between
          ${isResizing ? 'cursor-ew-resize border-blue-400 shadow-lg' : 'cursor-grab active:cursor-grabbing'}
        `}
        style={{
          width: getTaskWidth(),
          minWidth: '100px',
        }}
      >
        {/* DRAG AREA - SEPARATE FROM RESIZE HANDLE */}
        <div
          {...attributes}
          {...listeners}
          className="absolute inset-0 cursor-grab active:cursor-grabbing"
          style={{
            // Exclude the resize handle area from drag
            right: '12px',
            pointerEvents: isResizing ? 'none' : 'auto',
          }}
        />


        {/* Task Content - COMPACT DESIGN */}
        <div className="relative pointer-events-none select-none">
          <div className="flex items-start justify-between mb-1">
            <h4
              className="font-semibold text-gray-800 line-clamp-1 leading-tight"
              style={{ fontSize: `${0.75 * textZoom}rem` }}
            >
              {task.title}
            </h4>
            {task.project && (
              <div
                className="w-2 h-2 rounded-full flex-shrink-0 ml-2 mt-1"
                style={{ backgroundColor: task.project.color || '#64748b' }}
              />
            )}
          </div>

          {task.content && (
            <p className="text-xs text-gray-600 line-clamp-1 mb-1" style={{ fontSize: `${0.65 * textZoom}rem` }}>
              {task.content}
            </p>
          )}

          <div className="flex items-center justify-between">
            <div className="flex items-center gap-1">
              {task.project && (
                <span className="text-xs text-gray-500 font-medium" style={{ fontSize: `${0.6 * textZoom}rem` }}>
                  {task.project.name}
                </span>
              )}
              {task.tag && (
                <TagBadge tag={task.tag} size="sm" />
              )}
            </div>

            {task.scheduledTime && (
              <span className="text-xs text-gray-400" style={{ fontSize: `${0.6 * textZoom}rem` }}>
                {new Date(task.scheduledTime).toLocaleTimeString('en-US', {
                  hour: 'numeric',
                  minute: '2-digit',
                })}
              </span>
            )}
          </div>
        </div>

        {/* Status indicator */}
        <div className={`
          absolute top-2 right-2 w-2 h-2 rounded-full
          ${task.status === 'IN_PROGRESS' ? 'bg-blue-500' :
            task.status === 'DONE' ? 'bg-green-500' :
            task.status === 'PAUSED' ? 'bg-yellow-500' : 'bg-gray-400'}
        `} />

        {/* COMPLETELY SEPARATED RESIZE HANDLE - NO DRAG INTERFERENCE */}
        <div
          className={`
            absolute right-0 top-0 bottom-0 w-3 transition-all duration-200 z-40
            ${isResizing ? 'bg-blue-200 opacity-100' : 'bg-transparent hover:bg-blue-100 group-hover:opacity-100 opacity-0'}
            cursor-ew-resize
          `}
          onMouseDown={handleResizeStart}
          style={{
            pointerEvents: 'auto',
            borderRadius: '0 8px 8px 0',
          }}
          title="Drag to resize task duration"
        >
          <div className={`
            absolute right-0.5 top-1/2 transform -translate-y-1/2 w-0.5 h-6 rounded-full transition-opacity
            ${isResizing ? 'bg-blue-600 opacity-100' : 'bg-blue-400 opacity-0 group-hover:opacity-100'}
          `} />
        </div>
      </div>
    </div>
  );
};

// Beautiful Timeline Slot Component
const TimelineSlot: React.FC<{
  slot: any;
  tasks: Task[];
  viewMode: ViewMode;
  textZoom: number;
  onTaskRightClick?: (e: React.MouseEvent, task: Task) => void;
  onTaskResize?: (taskId: string, newDuration: number) => void;
}> = ({ slot, tasks, viewMode, textZoom, onTaskRightClick, onTaskResize }) => {
  const { setNodeRef, isOver } = useDroppable({
    id: slot.id,
  });

  const isCurrentTime = () => {
    if (['15min', '30min', '1hr'].includes(viewMode)) {
      const now = new Date();
      if (viewMode === '15min' || viewMode === '30min') {
        return now.getHours() === slot.hour &&
               (slot.minute || 0) <= now.getMinutes() &&
               (slot.minute || 0) + (viewMode === '15min' ? 15 : 30) > now.getMinutes() &&
               slot.date.toDateString() === now.toDateString();
      } else {
        return slot.hour === now.getHours() && slot.date.toDateString() === now.toDateString();
      }
    } else {
      return slot.isToday;
    }
  };

  const isPastTime = () => {
    const now = new Date();
    if (['15min', '30min', '1hr'].includes(viewMode)) {
      if (viewMode === '15min' || viewMode === '30min') {
        const slotEndTime = new Date(slot.date);
        slotEndTime.setHours(slot.hour, (slot.minute || 0) + (viewMode === '15min' ? 15 : 30));
        return slotEndTime < now;
      } else {
        const slotEndTime = new Date(slot.date);
        slotEndTime.setHours(slot.hour + 1, 0);
        return slotEndTime < now;
      }
    } else {
      return slot.date.toDateString() < now.toDateString();
    }
  };

  const getSlotWidth = () => {
    let baseWidth;
    switch (viewMode) {
      case '15min':
      case '30min':
        baseWidth = 120; // Reduced from 200 to 120
        break;
      case '1hr':
        baseWidth = 150; // Reduced from 250 to 150
        break;
      case '4hr':
        baseWidth = 180; // Reduced from 300 to 180
        break;
      case '8hr':
        baseWidth = 210; // Reduced from 350 to 210
        break;
      case 'daily':
      default:
        baseWidth = 168; // Reduced from 280 to 168
        break;
    }
    return baseWidth * textZoom; // Apply text zoom to horizontal spacing
  };

  const getSlotHeight = () => {
    return 350; // Reduced from 500 to 350 for better 1080p compatibility
  };

  return (
    <div
      ref={setNodeRef}
      className={`
        relative border-r border-gray-200 transition-all duration-200 flex-shrink-0
        bg-gradient-to-b from-gray-50 to-white
        ${isOver ? 'bg-blue-50 border-blue-300 shadow-inner' : 'hover:bg-gray-100'}
        ${isCurrentTime() ? 'bg-gradient-to-b from-blue-100 to-blue-50 border-blue-400' : ''}
      `}
      style={{
        width: `${getSlotWidth()}px`,
        height: `${getSlotHeight()}px`,
        minHeight: `${getSlotHeight()}px`,
      }}
    >
      {/* Time Label - COMPACT DESIGN */}
      <div className={`
        absolute top-0 left-0 right-0 z-30 p-2 border-b border-gray-200 bg-white/95 backdrop-blur-sm
        ${isCurrentTime() ? 'bg-blue-100/95 border-blue-300' : ''}
        ${isPastTime() ? 'bg-gray-100/95 text-gray-400' : ''}
      `}>
        <div
          className={`font-semibold text-center ${isPastTime() ? 'text-gray-400' : 'text-gray-700'}`}
          style={{ fontSize: `${0.7 * textZoom}rem` }}
        >
          {slot.label}
        </div>
        {isCurrentTime() && (
          <div
            className="text-blue-600 text-center mt-0.5 font-medium"
            style={{ fontSize: `${0.6 * textZoom}rem` }}
          >
            Current Time
          </div>
        )}
      </div>

      {/* Current time indicator */}
      {isCurrentTime() && (
        <div className="absolute top-12 left-0 w-full h-1 bg-blue-500 z-10 shadow-sm" />
      )}

      {/* Tasks Container - SIMPLE Y-AXIS STACKING */}
      <div className="p-2 min-h-[200px] mt-10 overflow-visible">
        <div className="space-y-2">
          {tasks.map((task, index) => (
            <div
              key={task.id}
              className="relative"
              style={{
                minWidth: 'max-content',
                // Simple Y-axis stacking - each task gets its own row
                zIndex: 10 - index, // Higher tasks have higher z-index
              }}
            >
              <TimelineTask
                task={task}
                textZoom={textZoom}
                viewMode={viewMode}
                onRightClick={onTaskRightClick}
                onResize={onTaskResize}
              />
            </div>
          ))}

          {/* Drop indicator */}
          {isOver && tasks.length === 0 && (
            <div className="border-2 border-dashed border-blue-400 rounded-xl p-6 bg-blue-50/50">
              <div className="text-center">
                <Plus className="h-6 w-6 text-blue-400 mx-auto mb-1" />
                <p className="text-xs font-medium text-blue-600">Drop task here</p>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

// Main HorizontalTimeline Component
const HorizontalTimeline: React.FC<TimelineProps> = ({ currentDate, onDateChange }) => {
  const { tasks, updateTask } = useTaskStore();
  const { projects } = useProjectStore();
  const { tags } = useTagStore();

  // Core timeline state
  const [viewMode, setViewMode] = useState<ViewMode>('1hr');
  const [selectedProject, setSelectedProject] = useState<string>('all');
  const [selectedTag, setSelectedTag] = useState<string>('all');
  const [draggedTask, setDraggedTask] = useState<Task | null>(null);
  const [autoFollow, setAutoFollow] = useState<boolean>(true);

  // Text and spacing zoom functionality
  const [textZoom, setTextZoom] = useState(1);

  // Pan functionality
  const [isPanning, setIsPanning] = useState(false);
  const [panStart, setPanStart] = useState({ x: 0, scrollLeft: 0 });

  // Context menu functionality
  const [contextMenu, setContextMenu] = useState<{
    task: Task;
    x: number;
    y: number;
  } | null>(null);

  const timelineRef = useRef<HTMLDivElement>(null);

  // PROFESSIONAL SENSORS - OPTIMIZED FOR ACCURACY AND RESPONSIVENESS
  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 3, // Very responsive for precise control
        tolerance: 5,
      },
    })
  );

  // Generate time slots based on view mode
  const timeSlots = useMemo(() => {
    return generateTimeSlots(currentDate, viewMode);
  }, [viewMode, currentDate]);

  // Auto-follow current time
  useEffect(() => {
    if (!autoFollow || !timelineRef.current) return;

    const scrollToCurrentTime = () => {
      const now = new Date();
      if (currentDate.toDateString() !== now.toDateString()) return;

      const timeSlotIndex = timeSlots.findIndex(slot => {
        if (['15min', '30min', '1hr'].includes(viewMode)) {
          if (viewMode === '15min' || viewMode === '30min') {
            return slot.hour === now.getHours() &&
                   (slot.minute || 0) <= now.getMinutes() &&
                   (slot.minute || 0) + (viewMode === '15min' ? 15 : 30) > now.getMinutes();
          } else {
            return slot.hour === now.getHours();
          }
        } else {
          return slot.isToday;
        }
      });

      if (timeSlotIndex >= 0 && timelineRef.current) {
        const slotWidth = viewMode === '15min' || viewMode === '30min' ? 120 :
                         viewMode === '1hr' ? 150 :
                         viewMode === '4hr' ? 180 :
                         viewMode === '8hr' ? 210 : 168;
        // Position current time at the beginning (left side) of the visible area
        const scrollPosition = timeSlotIndex * slotWidth * textZoom;
        timelineRef.current.scrollTo({
          left: Math.max(0, scrollPosition),
          behavior: 'smooth'
        });
      }
    };

    scrollToCurrentTime();
    const interval = setInterval(scrollToCurrentTime, 60000);
    return () => clearInterval(interval);
  }, [timeSlots, viewMode, currentDate, autoFollow]);

  // Filter tasks
  const filteredTasks = useMemo(() => {
    return tasks.filter(task => {
      if (selectedProject !== 'all' && task.projectId !== selectedProject) {
        return false;
      }
      if (selectedTag !== 'all' && task.tagId !== selectedTag) {
        return false;
      }
      return true;
    });
  }, [tasks, selectedProject, selectedTag]);

  // SIMPLE: Get tasks for specific slot - ORIGINAL APPROACH, NO DUPLICATES
  const getTasksForSlot = (slot: any) => {
    return filteredTasks.filter(task => {
      if (!task.scheduledTime) return false;
      if (!['TODO', 'IN_PROGRESS'].includes(task.status)) return false;

      const taskDate = new Date(task.scheduledTime);

      switch (viewMode) {
        case '15min':
        case '30min':
          return taskDate.getHours() === slot.hour &&
                 taskDate.getMinutes() === (slot.minute || 0) &&
                 taskDate.toDateString() === slot.date.toDateString();

        case '1hr':
          return taskDate.getHours() === slot.hour &&
                 taskDate.toDateString() === slot.date.toDateString();

        case '4hr':
        case '8hr':
          const slotStart = slot.hour;
          const slotEnd = slot.hour + (slot.duration || 1);
          return taskDate.getHours() >= slotStart &&
                 taskDate.getHours() < slotEnd &&
                 taskDate.toDateString() === slot.date.toDateString();

        case 'daily':
        default:
          return taskDate.toDateString() === slot.date.toDateString();
      }
    });
  };

  // Categorize tasks by status for sidebar
  const unscheduledTasks = filteredTasks.filter(task =>
    !task.scheduledTime && task.status === 'TODO'
  );

  const pausedTasks = filteredTasks.filter(task =>
    task.status === 'PAUSED'
  );

  const completedTasks = filteredTasks.filter(task =>
    task.status === 'DONE'
  );

  const archivedTasks = filteredTasks.filter(task =>
    task.status === 'ARCHIVED'
  );

  // SIMPLE DRAG START HANDLER
  const handleDragStart = (event: DragStartEvent) => {
    const task = tasks.find(t => t.id === event.active.id);
    setDraggedTask(task || null);
  };

  // SIMPLE DRAG END HANDLER
  const handleDragEnd = async (event: DragEndEvent) => {
    const { active, over } = event;
    setDraggedTask(null);

    if (!over) return;

    const taskId = active.id as string;
    const dropZoneId = over.id as string;

    try {
      // Handle section drops - remove from timeline and update status
      if (dropZoneId === 'unscheduled-section') {
        await updateTask(taskId, {
          status: 'TODO',
          scheduledTime: undefined,
        });
        return;
      }

      if (dropZoneId === 'paused-section') {
        await updateTask(taskId, {
          status: 'PAUSED',
          scheduledTime: undefined,
        });
        return;
      }

      if (dropZoneId === 'completed-section') {
        await updateTask(taskId, {
          status: 'DONE',
          scheduledTime: undefined,
        });
        return;
      }

      if (dropZoneId === 'archived-section') {
        await updateTask(taskId, {
          status: 'ARCHIVED',
          scheduledTime: undefined,
        });
        return;
      }

      // Handle timeline slot drops with PERFECT date handling
      let scheduledTime: Date;

      // Find the corresponding slot to get the exact date and time
      const targetSlot = timeSlots.find(slot => slot.id === dropZoneId);
      if (!targetSlot) {
        console.error('Target slot not found:', dropZoneId);
        return;
      }

      // Use the slot's date object directly for perfect accuracy
      scheduledTime = new Date(targetSlot.date);

      // For time-based views, set the exact hour and minute
      if (['15min', '30min', '1hr'].includes(viewMode)) {
        scheduledTime.setHours(targetSlot.hour || 0);
        scheduledTime.setMinutes(targetSlot.minute || 0);
        scheduledTime.setSeconds(0);
        scheduledTime.setMilliseconds(0);
      } else if (['4hr', '8hr'].includes(viewMode)) {
        // For multi-hour views, use the slot's starting hour
        scheduledTime.setHours(targetSlot.hour || 0);
        scheduledTime.setMinutes(0);
        scheduledTime.setSeconds(0);
        scheduledTime.setMilliseconds(0);
      } else {
        // For daily views, set to 9 AM
        scheduledTime.setHours(9, 0, 0, 0);
      }

      // Get the current task to check if it needs a default duration
      const currentTask = tasks.find(t => t.id === taskId);
      const updateData: any = {
        status: 'IN_PROGRESS',
        scheduledTime: scheduledTime.toISOString(),
      };

      // Set default 1-hour duration if task doesn't have effortEstimate
      if (!currentTask?.effortEstimate) {
        updateData.effortEstimate = 60; // Default 1 hour in minutes
      }

      await updateTask(taskId, updateData);
    } catch (error) {
      console.error('Failed to update task:', error);
    }
  };

  const navigateTime = (direction: 'prev' | 'next') => {
    const newDate = new Date(currentDate);
    if (['15min', '30min', '1hr', '4hr', '8hr'].includes(viewMode)) {
      newDate.setDate(newDate.getDate() + (direction === 'next' ? 1 : -1));
    } else {
      newDate.setDate(newDate.getDate() + (direction === 'next' ? 7 : -7));
    }
    onDateChange(newDate);
  };

  // Proper text and spacing zoom functionality with position focus
  const handleWheel = (e: React.WheelEvent) => {
    e.preventDefault();

    if (!timelineRef.current) return;

    // Get mouse position relative to timeline
    const rect = timelineRef.current.getBoundingClientRect();
    const mouseX = e.clientX - rect.left;
    const scrollLeft = timelineRef.current.scrollLeft;

    // Calculate the content position under the mouse
    const contentX = scrollLeft + mouseX;

    // Text zoom speed
    const zoomSpeed = 0.05;
    const delta = -e.deltaY; // Invert for natural zoom direction

    setTextZoom(prevZoom => {
      const newZoom = Math.max(0.8, Math.min(1.5, prevZoom + (delta > 0 ? zoomSpeed : -zoomSpeed)));

      // Maintain focus on the mouse position
      if (timelineRef.current) {
        const zoomRatio = newZoom / prevZoom;
        const newContentX = contentX * zoomRatio;
        const newScrollLeft = newContentX - mouseX;

        // Apply the new scroll position after a brief delay to allow zoom to apply
        setTimeout(() => {
          if (timelineRef.current) {
            timelineRef.current.scrollLeft = Math.max(0, newScrollLeft);
          }
        }, 10);
      }

      return newZoom;
    });
  };

  // Pan event handlers - LEFT CLICK ONLY
  const handleMouseDown = (e: React.MouseEvent) => {
    // Only respond to left mouse button (button 0)
    if (e.button !== 0) return;

    // Only start panning if not clicking on a draggable task
    if ((e.target as HTMLElement).closest('[data-rbd-draggable-id], [data-rbd-droppable-id]')) {
      return;
    }

    if (!timelineRef.current) return;

    setIsPanning(true);
    setPanStart({
      x: e.clientX,
      scrollLeft: timelineRef.current.scrollLeft,
    });

    // Prevent text selection during pan
    e.preventDefault();
  };

  const handleMouseMove = (e: React.MouseEvent) => {
    if (!isPanning || !timelineRef.current) return;

    e.preventDefault();
    const deltaX = e.clientX - panStart.x;
    timelineRef.current.scrollLeft = panStart.scrollLeft - deltaX;
  };

  const handleMouseUp = () => {
    setIsPanning(false);
  };

  const handleMouseLeave = () => {
    setIsPanning(false);
  };

  // Task resize handler
  const handleTaskResize = async (taskId: string, newDuration: number) => {
    try {
      await updateTask(taskId, { effortEstimate: newDuration });
    } catch (error) {
      console.error('Failed to update task duration:', error);
    }
  };

  // Context menu handlers
  const handleTaskRightClick = (e: React.MouseEvent, task: Task) => {
    e.preventDefault();
    e.stopPropagation();

    setContextMenu({
      task,
      x: e.clientX,
      y: e.clientY,
    });
  };

  // Close context menu when clicking outside
  useEffect(() => {
    const handleClickOutside = () => {
      setContextMenu(null);
    };

    if (contextMenu) {
      document.addEventListener('click', handleClickOutside);
      return () => document.removeEventListener('click', handleClickOutside);
    }
  }, [contextMenu]);

  const handleContextMenuAction = async (action: string, task: Task) => {
    try {
      switch (action) {
        case 'edit':
          // TODO: Open edit modal
          console.log('Edit task:', task.title);
          break;
        case 'pause':
          await updateTask(task.id, { status: 'PAUSED' });
          break;
        case 'complete':
          await updateTask(task.id, { status: 'DONE' });
          break;
        case 'archive':
          await updateTask(task.id, { status: 'ARCHIVED' });
          break;
      }
    } catch (error) {
      console.error('Error updating task:', error);
    } finally {
      setContextMenu(null);
    }
  };

  const closeContextMenu = () => {
    setContextMenu(null);
  };

  // Close context menu on click outside
  React.useEffect(() => {
    const handleClickOutside = () => {
      if (contextMenu) {
        closeContextMenu();
      }
    };

    document.addEventListener('click', handleClickOutside);
    return () => document.removeEventListener('click', handleClickOutside);
  }, [contextMenu]);

  return (
    <DndContext
      sensors={sensors}
      collisionDetection={closestCorners}
      onDragStart={handleDragStart}
      onDragEnd={handleDragEnd}
    >
      <div className="h-full flex flex-col bg-gradient-to-br from-gray-50 to-white">
        {/* Beautiful Timeline Header */}
        <div className="flex-shrink-0 p-6 border-b border-gray-200 bg-white shadow-sm">
          <div className="flex items-center justify-between mb-6">
            <h1 className="text-2xl font-bold text-gray-900 flex items-center">
              <Calendar className="h-6 w-6 mr-3 text-blue-600" />
              Timeline
            </h1>

            {/* Beautiful View Mode Toggle */}
            <div className="flex items-center space-x-2 bg-gray-100 rounded-xl p-1">
              {(['15min', '30min', '1hr', '4hr', '8hr', 'daily'] as ViewMode[]).map((mode) => (
                <Button
                  key={mode}
                  variant={viewMode === mode ? 'default' : 'ghost'}
                  size="sm"
                  onClick={() => setViewMode(mode)}
                  className={`
                    transition-all duration-200 rounded-lg px-4 py-2
                    ${viewMode === mode
                      ? 'bg-white shadow-sm text-blue-600 font-semibold'
                      : 'text-gray-600 hover:text-gray-900 hover:bg-white/50'
                    }
                  `}
                >
                  {mode}
                </Button>
              ))}
            </div>
          </div>

          {/* Navigation and Filters */}
          <div className="flex items-center justify-between">
            {/* Beautiful Date Navigation */}
            <div className="flex items-center space-x-4">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => navigateTime('prev')}
                className="hover:bg-gray-100 rounded-lg p-2"
              >
                <ChevronLeft className="h-5 w-5" />
              </Button>

              <div className="text-center">
                <h2 className="text-lg font-semibold text-gray-900">
                  {['15min', '30min', '1hr', '4hr', '8hr'].includes(viewMode)
                    ? currentDate.toLocaleDateString('en-US', {
                        weekday: 'long',
                        month: 'long',
                        day: 'numeric',
                        year: 'numeric'
                      })
                    : `Week of ${timeSlots[0]?.label} - ${timeSlots[6]?.label}`
                  }
                </h2>
                <p className="text-sm text-gray-500">
                  {timeSlots.length} time slots
                </p>
              </div>

              <Button
                variant="ghost"
                size="sm"
                onClick={() => navigateTime('next')}
                className="hover:bg-gray-100 rounded-lg p-2"
              >
                <ChevronRight className="h-5 w-5" />
              </Button>
            </div>

            {/* Beautiful Filters */}
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2">
                <Filter className="h-4 w-4 text-gray-500" />
                <select
                  value={selectedProject}
                  onChange={(e) => setSelectedProject(e.target.value)}
                  className="text-sm border border-gray-300 rounded-lg px-3 py-2 bg-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="all">All Projects</option>
                  {projects.map((project) => (
                    <option key={project.id} value={project.id}>
                      {project.name}
                    </option>
                  ))}
                </select>
              </div>

              <select
                value={selectedTag}
                onChange={(e) => setSelectedTag(e.target.value)}
                className="text-sm border border-gray-300 rounded-lg px-3 py-2 bg-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="all">All Tags</option>
                {tags.map((tag) => (
                  <option key={tag.id} value={tag.id}>
                    {tag.name}
                  </option>
                ))}
              </select>

              <Button
                variant={autoFollow ? 'default' : 'outline'}
                size="sm"
                onClick={() => setAutoFollow(!autoFollow)}
                className="flex items-center space-x-2"
              >
                <Clock className="h-4 w-4" />
                <span>Follow</span>
              </Button>

              {/* Text Size Controls */}
              <div className="flex items-center space-x-2 border-l border-gray-300 pl-4">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setTextZoom(prev => Math.max(0.8, prev - 0.1))}
                  disabled={textZoom <= 0.8}
                  className="p-2"
                  title="Smaller Text"
                >
                  <ZoomOut className="h-4 w-4" />
                </Button>

                <div className="text-sm font-medium text-gray-700 min-w-[60px] text-center">
                  {Math.round(textZoom * 100)}%
                </div>

                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setTextZoom(prev => Math.min(1.5, prev + 0.1))}
                  disabled={textZoom >= 1.5}
                  className="p-2"
                  title="Larger Text"
                >
                  <ZoomIn className="h-4 w-4" />
                </Button>

                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setTextZoom(1)}
                  className="text-xs px-2"
                  title="Reset Text Size"
                >
                  Reset
                </Button>
              </div>
            </div>
          </div>
        </div>

        {/* Beautiful Timeline Content */}
        <div className="flex-1 flex flex-col overflow-hidden">
          {/* Beautiful Timeline Grid */}
          <div
            ref={timelineRef}
            className={`flex-1 overflow-x-auto overflow-y-hidden bg-gradient-to-b from-white to-gray-50 ${isPanning ? 'cursor-grabbing' : 'cursor-grab'}`}
            onScroll={() => setAutoFollow(false)}
            onWheel={handleWheel}
            onMouseDown={handleMouseDown}
            onMouseMove={handleMouseMove}
            onMouseUp={handleMouseUp}
            onMouseLeave={handleMouseLeave}
            style={{
              height: 'calc(100vh - 600px)', // Reduced height for better 1080p compatibility
              userSelect: isPanning ? 'none' : 'auto',
            }}
          >
            <div className="flex h-full min-w-max">
              {timeSlots.map((slot) => (
                <TimelineSlot
                  key={slot.id}
                  slot={slot}
                  tasks={getTasksForSlot(slot)}
                  viewMode={viewMode}
                  textZoom={textZoom}
                  onTaskRightClick={handleTaskRightClick}
                  onTaskResize={handleTaskResize}
                />
              ))}
            </div>
          </div>

          {/* Beautiful Horizontal Task Sections - Reduced Height */}
          <div className="h-64 border-t border-gray-200 bg-gradient-to-b from-gray-50 to-white p-3 overflow-y-auto">
            <div className="grid grid-cols-4 gap-4 h-full">
              <CollapsibleTaskSection
                id="unscheduled-section"
                title="Unscheduled Tasks"
                icon={<Clock className="h-4 w-4" />}
                tasks={unscheduledTasks}
                color="blue"
                description="Tasks waiting to be scheduled"
                defaultExpanded={true}
              />

              <CollapsibleTaskSection
                id="paused-section"
                title="Paused Tasks"
                icon={<Pause className="h-4 w-4" />}
                tasks={pausedTasks}
                color="yellow"
                description="Tasks temporarily paused"
                defaultExpanded={false}
              />

              <CollapsibleTaskSection
                id="completed-section"
                title="Completed Tasks"
                icon={<CheckCircle2 className="h-4 w-4" />}
                tasks={completedTasks}
                color="green"
                description="Finished tasks"
                defaultExpanded={false}
              />

              <CollapsibleTaskSection
                id="archived-section"
                title="Archived Tasks"
                icon={<Archive className="h-4 w-4" />}
                tasks={archivedTasks}
                color="gray"
                description="Archived tasks"
                defaultExpanded={false}
              />
            </div>
          </div>
        </div>

        {/* SIMPLE DRAG OVERLAY - CLEAN CENTERING */}
        <DragOverlay
          dropAnimation={{
            duration: 200,
            easing: 'cubic-bezier(0.18, 0.67, 0.6, 1.22)',
          }}
          style={{
            cursor: 'grabbing',
          }}
        >
          {draggedTask ? (
            <StandardizedDragPreview
              task={draggedTask}
              textZoom={textZoom}
              viewMode={viewMode}
            />
          ) : null}
        </DragOverlay>

        {/* Context Menu */}
        {contextMenu && (
          <div
            className="fixed z-50 bg-white border border-gray-200 rounded-lg shadow-lg py-2 min-w-[160px]"
            style={{
              left: contextMenu.x,
              top: contextMenu.y,
            }}
          >
            <button
              onClick={() => handleContextMenuAction('edit', contextMenu.task)}
              className="w-full px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-100 flex items-center gap-2"
            >
              <Edit3 className="h-4 w-4" />
              Edit Task
            </button>
            <button
              onClick={() => handleContextMenuAction('pause', contextMenu.task)}
              className="w-full px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-100 flex items-center gap-2"
            >
              <Pause className="h-4 w-4" />
              Pause
            </button>
            <button
              onClick={() => handleContextMenuAction('complete', contextMenu.task)}
              className="w-full px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-100 flex items-center gap-2"
            >
              <CheckCircle2 className="h-4 w-4" />
              Complete
            </button>
            <button
              onClick={() => handleContextMenuAction('archive', contextMenu.task)}
              className="w-full px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-100 flex items-center gap-2"
            >
              <Archive className="h-4 w-4" />
              Archive
            </button>
          </div>
        )}
      </div>
    </DndContext>
  );
};

export default HorizontalTimeline;